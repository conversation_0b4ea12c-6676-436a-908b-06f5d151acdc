# Multi-Account Telegram Userbot System

## Overview

This system has been upgraded to handle multiple Telegram user accounts with intelligent task distribution and rate limiting. Each account can process tasks independently while respecting Telegram's rate limits and avoiding conflicts.

## Key Features

### 🔄 Multi-Account Support
- Automatically discovers all `.session` files in the project directory
- Manages multiple Telegram user accounts simultaneously
- Each account operates independently

### ⏱️ Smart Rate Limiting
- **5-minute cooldown** between task batches for each account
- **Maximum 2 tasks** per account within each 5-minute window
- Prevents Telegram rate limiting and account restrictions

### 📊 Task Distribution
- Intelligent queue processing that distributes tasks across available accounts
- Accounts are marked as busy during task processing
- Failed tasks are automatically redistributed

### 🔍 Real-time Monitoring
- Live status monitoring showing account availability
- Task count tracking per account
- Cooldown timers and next available times

## Configuration

### Constants (in `run_user_bots.py`)
```python
TASK_COOLDOWN_MINUTES = 5      # Minutes to wait between task batches
MAX_TASKS_PER_COOLDOWN = 2     # Max tasks per account in cooldown period
QUEUE_CHECK_INTERVAL = 15      # Seconds between queue checks
```

### Account Discovery
The system automatically discovers accounts by scanning for `*.session` files:
- `account1.session` → `account1`
- `account2.session` → `account2`
- `default_session.session` → `default_session`

## How It Works

### 1. Account Management
```python
class AccountManager:
    - Discovers all session files
    - Tracks task history and cooldowns
    - Manages client connections
    - Distributes tasks fairly
```

### 2. Queue Processing
```python
async def process_queue():
    - Continuously monitors download_queue
    - Finds available accounts
    - Distributes tasks with rate limiting
    - Handles failures gracefully
```

### 3. Media Forwarding
```python
async def media_forwarder(client, message):
    - Receives media from @YuklaydiBot
    - Matches media to correct account task
    - Forwards to target bot
    - Tracks completion
```

## Usage

### Running the System
```bash
python manage.py run_user_bots
```

### Expected Output
```
Starting Telegram userbots...
🔍 Discovered account: account1
🔍 Discovered account: account2
🔍 Discovered account: default_session
✅ Started client for account: account1
✅ Account account1 is now listening for media replies...
✅ Started client for account2
✅ Account account2 is now listening for media replies...
🚀 Started 2 accounts. Queue processor is running...

==================================================
📊 ACCOUNT STATUS
==================================================
📱 account1: 🟢 FREE | Tasks: 0/2
📱 account2: 🟢 FREE | Tasks: 0/2
==================================================
```

### Task Processing Example
```
📤 [account1] Sent to @YuklaydiBot | msg_id: 12345
📦 [account1] Forwarded video | file_id: BAACAgQAA... | sending to chat_id: **********
✅ [account1] video sent to chat_id **********
```

## Account Status Monitoring

The system provides real-time status updates every minute:

- **🟢 FREE**: Account available for new tasks
- **🔴 BUSY**: Account currently processing a task
- **Tasks: X/Y**: Current tasks vs maximum allowed
- **⏰ Next available**: Time until account can accept new tasks

## Error Handling

### Account Connection Issues
- Failed accounts are skipped gracefully
- System continues with available accounts
- Connection errors are logged with account names

### Task Failures
- Failed tasks are returned to queue
- Accounts are marked as free after failures
- Retry logic prevents infinite loops

### Rate Limiting
- Automatic cooldown enforcement
- Task redistribution when accounts are busy
- Graceful degradation under high load

## Adding New Accounts

1. **Create Session File**: Add new `.session` file to project directory
2. **Authenticate**: Use Pyrogram to authenticate the account
3. **Restart System**: The new account will be automatically discovered

### Example: Adding account3
```bash
# Create session file (will need proper authentication)
touch account3.session

# Restart the userbot system
python manage.py run_user_bots
```

## Testing

### Create Test Accounts
```bash
python create_test_accounts.py
```

This creates empty session files for testing the multi-account logic.

## Troubleshooting

### No Accounts Available
```
⏳ No available accounts, waiting...
```
**Solution**: Wait for cooldown periods to expire or add more accounts

### Account Connection Failed
```
❌ Failed to start client for account2: [Error details]
```
**Solution**: Check session file validity and authentication

### Media Mismatch
```
⚠️ [account1] Media doesn't match account task (expected: account2)
```
**Solution**: Normal behavior - media is redistributed to correct account

## Performance Considerations

- **Memory**: Each account maintains a separate client connection
- **Rate Limits**: 5-minute cooldowns prevent Telegram restrictions
- **Scalability**: System can handle dozens of accounts efficiently
- **Monitoring**: Status updates help identify bottlenecks

## Security Notes

- Session files contain authentication tokens
- Keep session files secure and private
- Use `.gitignore` to exclude session files from version control
- Consider encryption for production deployments
