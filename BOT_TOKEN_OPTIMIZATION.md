# Bot Token Optimization

## Overview

The system has been optimized to automatically fetch bot username from bot token using Telegram Bot API's `getMe` method. This eliminates the need to manually provide both `bot_token` and `bot_username` in API requests.

## 🚀 Key Benefits

1. **Reduced API Payload**: Only `bot_token` is required, `bot_username` is optional
2. **Automatic Validation**: Bot token is validated during the process
3. **Error Prevention**: Eliminates bot_token/bot_username mismatch errors
4. **Performance**: Results are cached for better performance
5. **Backward Compatible**: Still accepts `bot_username` if provided

## 📡 API Usage Examples

### Before Optimization (Required both fields)
```json
{
    "hosting": "instagram",
    "url": "https://www.instagram.com/reel/DJvRNpKIp0X/?igsh=bXVpNjBzdWl0ZzU3",
    "bot_username": "@demorepresentrobot",
    "bot_token": "7940308044:AAGn8QHcLdNcFdZCINV7LVONzP8xy619cJY",
    "chat_id": "2105729169"
}
```

### After Optimization (bot_username is optional)
```json
{
    "hosting": "instagram",
    "url": "https://www.instagram.com/reel/DJvRNpKIp0X/?igsh=bXVpNjBzdWl0ZzU3",
    "bot_token": "7940308044:AAGn8QHcLdNcFdZCINV7LVONzP8xy619cJY",
    "chat_id": "2105729169"
}
```

### cURL Example
```bash
curl --location 'http://127.0.0.1:8000/v1/insta-dl/' \
--header 'Content-Type: application/json' \
--data-raw '{
    "hosting": "instagram",
    "url": "https://www.instagram.com/reel/DJvRNpKIp0X/?igsh=bXVpNjBzdWl0ZzU3",
    "bot_token": "7940308044:AAGn8QHcLdNcFdZCINV7LVONzP8xy619cJY",
    "chat_id": "2105729169"
}'
```

## 🛠️ Technical Implementation

### New Utility Module: `bots/utils.py`

```python
from bots.utils import TelegramBotUtils

# Get bot username from token
username = TelegramBotUtils.get_bot_username(bot_token)

# Get full bot details
details = TelegramBotUtils.get_bot_details(bot_token)

# Validate bot token
is_valid = TelegramBotUtils.validate_bot_token(bot_token)
```

### Updated Serializer: `instagram/serializers.py`

The `DownloadSerializer` now:
- Makes `bot_username` optional (`required=False`)
- Automatically fetches `bot_username` if not provided
- Uses caching for better performance
- Provides detailed error messages

### Enhanced Processing: `userbots/management/commands/run_user_bots.py`

The task processing logic now:
- Handles missing `bot_username` in task data
- Auto-fetches username from token when needed
- Provides better logging and error handling

## 🧪 Testing

### Management Command
```bash
# Test a bot token
python manage.py test_bot_token 7940308044:AAGn8QHcLdNcFdZCINV7LVONzP8xy619cJY

# Validate only
python manage.py test_bot_token YOUR_BOT_TOKEN --validate-only

# Disable cache
python manage.py test_bot_token YOUR_BOT_TOKEN --no-cache
```

### Test Script
```bash
python test_bot_optimization.py
```

## 🔧 Configuration

### Caching
Bot information is cached for 1 hour by default to improve performance:
- Cache key format: `bot_info_{first_10_chars_of_token}...`
- Timeout: 3600 seconds (1 hour)
- Can be disabled by setting `use_cache=False`

### Error Handling
The system handles various error scenarios:
- Invalid bot token format
- Network connectivity issues
- Telegram API errors (401, 404, etc.)
- Timeout errors
- Bot not found errors

## 📊 Performance Impact

### Before
- 2 API fields required
- Manual validation needed
- Risk of token/username mismatch
- No caching

### After
- 1 API field required (50% reduction)
- Automatic validation
- Zero mismatch risk
- Intelligent caching
- Better error messages

## 🔄 Migration Guide

### For API Clients
1. **No changes required** - existing requests with both fields still work
2. **Optional optimization** - remove `bot_username` from requests
3. **Error handling** - update to handle new validation error messages

### For Developers
1. Use `bots.utils.TelegramBotUtils` for bot operations
2. Import and use utility functions for consistency
3. Leverage caching for better performance

## 🚨 Error Scenarios

### Invalid Bot Token
```json
{
    "bot_username": ["Invalid bot token or unable to fetch bot info: Telegram API error: Unauthorized"]
}
```

### Network Issues
```json
{
    "bot_username": ["Invalid bot token or unable to fetch bot info: Network error: Connection timeout"]
}
```

### Bot Not Found
```json
{
    "bot_username": ["Invalid bot token or unable to fetch bot info: Bot not found - Invalid token"]
}
```

## 🔮 Future Enhancements

1. **Database Storage**: Store bot info in database for offline access
2. **Webhook Validation**: Validate webhook URLs using bot token
3. **Rate Limiting**: Implement rate limiting for bot API calls
4. **Monitoring**: Add metrics for bot token usage and errors
5. **Bulk Operations**: Support bulk bot token validation

## 📝 Notes

- Bot information is fetched from `https://api.telegram.org/bot{token}/getMe`
- Caching reduces API calls and improves response time
- All existing functionality remains unchanged
- The optimization is transparent to end users
