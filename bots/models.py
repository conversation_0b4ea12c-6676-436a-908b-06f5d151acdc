from django.db import models


class TargetBots(models.Model):
    """
    target bots
    """
    name = models.Char<PERSON>ield(max_length=100)
    username = models.CharField(max_length=100)
    is_active = models.BooleanField(default=False)
    can_dl_from_insta = models.BooleanField(default=False)
    can_dl_from_tiktok = models.BooleanField(default=False)
    can_dl_from_youtube = models.BooleanField(default=False)
    is_queued = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.name} - ({self.username})"
    
    @classmethod
    def get_bots(cls):
        return cls.objects.all()

    @classmethod
    def get_available_bots(cls):
        return cls.objects.filter(is_active=True, is_queued=False)

    @classmethod
    def get_bot_by_username(cls, username: str):
        return cls.objects.get(username=username)

    def mark_as_queued(self):
        self.is_queued = True
        self.save()

    def mark_as_available(self):
        self.is_queued = False
        self.save()

