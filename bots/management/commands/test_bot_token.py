"""
Management command to test bot token optimization
"""
from django.core.management.base import BaseCommand, CommandError
from bots.utils import TelegramBotUtils


class Command(BaseCommand):
    help = "Test bot token functionality and get bot information"

    def add_arguments(self, parser):
        parser.add_argument(
            'bot_token',
            type=str,
            help='Telegram bot token to test'
        )
        parser.add_argument(
            '--no-cache',
            action='store_true',
            help='Disable caching for this request'
        )
        parser.add_argument(
            '--validate-only',
            action='store_true',
            help='Only validate the token without showing details'
        )

    def handle(self, *args, **options):
        bot_token = options['bot_token']
        use_cache = not options.get('no_cache', False)
        validate_only = options.get('validate_only', False)

        self.stdout.write("\n" + "="*60)
        self.stdout.write("🤖 BOT TOKEN TESTING")
        self.stdout.write("="*60)

        # Validate token format
        if not bot_token or ':' not in bot_token:
            raise CommandError("❌ Invalid bot token format. Expected format: 'bot_id:token'")

        try:
            if validate_only:
                # Just validate the token
                self.stdout.write("🔍 Validating bot token...")
                is_valid = TelegramBotUtils.validate_bot_token(bot_token)
                
                if is_valid:
                    self.stdout.write(
                        self.style.SUCCESS("✅ Bot token is valid!")
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR("❌ Bot token is invalid!")
                    )
            else:
                # Get full bot details
                self.stdout.write("🔍 Fetching bot information...")
                bot_details = TelegramBotUtils.get_bot_details(bot_token, use_cache)
                
                if not bot_details:
                    raise CommandError("❌ Could not fetch bot information")

                # Display bot information
                self.stdout.write(
                    self.style.SUCCESS("✅ Bot information retrieved successfully!")
                )
                self.stdout.write("\n📋 Bot Details:")
                self.stdout.write(f"   🆔 Bot ID: {bot_details.get('id', 'N/A')}")
                self.stdout.write(f"   👤 Name: {bot_details.get('first_name', 'N/A')}")
                self.stdout.write(f"   🏷️ Username: {bot_details.get('formatted_username', 'N/A')}")
                self.stdout.write(f"   🤖 Is Bot: {bot_details.get('is_bot', False)}")
                self.stdout.write(f"   👥 Can Join Groups: {bot_details.get('can_join_groups', False)}")
                self.stdout.write(f"   📖 Can Read All Messages: {bot_details.get('can_read_all_group_messages', False)}")
                self.stdout.write(f"   🔍 Supports Inline: {bot_details.get('supports_inline_queries', False)}")

                # Show optimization benefit
                username = bot_details.get('formatted_username')
                if username:
                    self.stdout.write("\n💡 Optimization Benefit:")
                    self.stdout.write("   Instead of providing both bot_token AND bot_username,")
                    self.stdout.write("   you can now provide only bot_token and the system will")
                    self.stdout.write(f"   automatically fetch the username: {username}")

                # Show cache status
                cache_status = "enabled" if use_cache else "disabled"
                self.stdout.write(f"\n🗄️ Cache: {cache_status}")

        except Exception as e:
            raise CommandError(f"❌ Error: {str(e)}")

        self.stdout.write("\n" + "="*60)
        self.stdout.write("🎯 Usage Examples:")
        self.stdout.write("="*60)
        self.stdout.write("# Test with your bot token:")
        self.stdout.write(f"python manage.py test_bot_token {bot_token[:20]}...")
        self.stdout.write("")
        self.stdout.write("# Validate only:")
        self.stdout.write(f"python manage.py test_bot_token {bot_token[:20]}... --validate-only")
        self.stdout.write("")
        self.stdout.write("# Disable cache:")
        self.stdout.write(f"python manage.py test_bot_token {bot_token[:20]}... --no-cache")
        self.stdout.write("")
        self.stdout.write("📡 API Request Example (optimized):")
        self.stdout.write("curl -X POST http://127.0.0.1:8000/v1/insta-dl/ \\")
        self.stdout.write("  -H 'Content-Type: application/json' \\")
        self.stdout.write("  -d '{")
        self.stdout.write('    "hosting": "instagram",')
        self.stdout.write('    "url": "https://www.instagram.com/reel/...",')
        self.stdout.write(f'    "bot_token": "{bot_token}",')
        self.stdout.write('    "chat_id": "your_chat_id"')
        self.stdout.write("  }'")
        self.stdout.write("")
        self.stdout.write("ℹ️ Note: bot_username is now optional and will be auto-fetched!")
        self.stdout.write("="*60)
