from bots.models import TargetBots
from asgiref.sync import sync_to_async


class BotsService:
    @staticmethod
    async def get_target_bots() -> list[TargetBots]:
        return await sync_to_async(list)(TargetBots.get_bots())

    @staticmethod
    async def get_available_bots() -> list[TargetBots]:
        return await sync_to_async(list)(TargetBots.get_available_bots())

    @staticmethod
    async def get_bot_by_username(username: str) -> TargetBots:
        return await sync_to_async(TargetBots.get_bot_by_username)(username)
