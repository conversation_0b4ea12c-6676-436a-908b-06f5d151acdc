# Generated by Django 5.2.4 on 2025-07-05 03:42

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TargetBots',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ('username', models.Char<PERSON>ield(max_length=100)),
                ('is_active', models.BooleanField(default=False)),
                ('can_dl_from_insta', models.BooleanField(default=False)),
                ('can_dl_from_tiktok', models.BooleanField(default=False)),
                ('can_dl_from_youtube', models.BooleanField(default=False)),
            ],
        ),
    ]
