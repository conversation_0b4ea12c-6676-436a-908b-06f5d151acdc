"""
Utility functions for working with Telegram bots
"""
import requests
from typing import Optional, Dict, Any
from django.core.cache import cache


class TelegramBotUtils:
    """Utility class for Telegram Bot API operations"""
    
    @staticmethod
    def get_bot_info(bot_token: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """
        Get bot information from Telegram Bot API using bot token
        
        Args:
            bot_token: Telegram bot token
            use_cache: Whether to use Django cache for bot info (default: True)
            
        Returns:
            Dict containing bot information or None if failed
            
        Raises:
            Exception: If API request fails or returns error
        """
        if not bot_token:
            raise ValueError("Bot token is required")
        
        # Check cache first if enabled
        cache_key = f"bot_info_{bot_token[:10]}..."  # Use first 10 chars for security
        if use_cache:
            cached_info = cache.get(cache_key)
            if cached_info:
                return cached_info
        
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result.get('ok'):
                bot_info = result.get('result')
                
                # Cache the result for 1 hour if successful
                if use_cache and bot_info:
                    cache.set(cache_key, bot_info, timeout=3600)
                
                return bot_info
            else:
                error_msg = result.get('description', 'Unknown error')
                raise Exception(f"Telegram API error: {error_msg}")
                
        except requests.exceptions.Timeout:
            raise Exception("Request timeout - Telegram API is not responding")
        except requests.exceptions.ConnectionError:
            raise Exception("Connection error - Unable to reach Telegram API")
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                raise Exception("Invalid bot token - Unauthorized")
            elif e.response.status_code == 404:
                raise Exception("Bot not found - Invalid token")
            else:
                raise Exception(f"HTTP error {e.response.status_code}: {e.response.text}")
        except requests.exceptions.RequestException as e:
            raise Exception(f"Network error: {str(e)}")
        except Exception as e:
            raise Exception(f"Error fetching bot info: {str(e)}")
    
    @staticmethod
    def get_bot_username(bot_token: str, use_cache: bool = True) -> Optional[str]:
        """
        Get bot username from bot token
        
        Args:
            bot_token: Telegram bot token
            use_cache: Whether to use Django cache (default: True)
            
        Returns:
            Bot username with @ prefix or None if not found
        """
        try:
            bot_info = TelegramBotUtils.get_bot_info(bot_token, use_cache)
            if bot_info and bot_info.get('username'):
                return f"@{bot_info['username']}"
            return None
        except Exception:
            return None
    
    @staticmethod
    def validate_bot_token(bot_token: str) -> bool:
        """
        Validate if bot token is valid by making a test API call
        
        Args:
            bot_token: Telegram bot token to validate
            
        Returns:
            True if token is valid, False otherwise
        """
        try:
            bot_info = TelegramBotUtils.get_bot_info(bot_token, use_cache=False)
            return bot_info is not None
        except Exception:
            return False
    
    @staticmethod
    def get_bot_details(bot_token: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        Get comprehensive bot details including username, name, and other info
        
        Args:
            bot_token: Telegram bot token
            use_cache: Whether to use Django cache (default: True)
            
        Returns:
            Dict with bot details: username, first_name, id, etc.
        """
        bot_info = TelegramBotUtils.get_bot_info(bot_token, use_cache)
        if not bot_info:
            return {}
        
        return {
            'id': bot_info.get('id'),
            'is_bot': bot_info.get('is_bot', False),
            'first_name': bot_info.get('first_name', ''),
            'username': bot_info.get('username', ''),
            'formatted_username': f"@{bot_info.get('username', '')}" if bot_info.get('username') else '',
            'can_join_groups': bot_info.get('can_join_groups', False),
            'can_read_all_group_messages': bot_info.get('can_read_all_group_messages', False),
            'supports_inline_queries': bot_info.get('supports_inline_queries', False),
        }


def get_bot_username_from_token(bot_token: str) -> Optional[str]:
    """
    Convenience function to get bot username from token
    
    Args:
        bot_token: Telegram bot token
        
    Returns:
        Bot username with @ prefix or None if failed
    """
    return TelegramBotUtils.get_bot_username(bot_token)


def validate_bot_token(bot_token: str) -> bool:
    """
    Convenience function to validate bot token
    
    Args:
        bot_token: Telegram bot token
        
    Returns:
        True if valid, False otherwise
    """
    return TelegramBotUtils.validate_bot_token(bot_token)
