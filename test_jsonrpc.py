#!/usr/bin/env python3
"""
Test script for JSONRPC API endpoints.
"""
import json
import requests


def test_jsonrpc_endpoint():
    """Test the JSONRPC endpoint with various methods."""
    
    base_url = "http://127.0.0.1:8000/jsonrpc/"
    headers = {
        "Content-Type": "application/json",
        "X-AUTH": "your-auth-token-here"  # Update this with your actual token
    }
    
    # Test 1: Get API documentation (GET request)
    print("🔍 Testing GET request for API documentation...")
    try:
        response = requests.get(base_url, headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ API documentation retrieved successfully")
            print(json.dumps(response.json(), indent=2)[:500] + "...")
        else:
            print(f"❌ Failed to get documentation: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "="*60 + "\n")
    
    # Test 2: Validate bot token
    print("🔍 Testing validate_bot_token method...")
    test_request = {
        "jsonrpc": "2.0",
        "method": "validate_bot_token",
        "params": {
            "bot_token": "1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZ"  # Fake token for testing
        },
        "id": 1
    }
    
    try:
        response = requests.post(base_url, json=test_request, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "="*60 + "\n")
    
    # Test 3: Get queue status
    print("🔍 Testing get_queue_status method...")
    test_request = {
        "jsonrpc": "2.0",
        "method": "get_queue_status",
        "params": {},
        "id": 2
    }
    
    try:
        response = requests.post(base_url, json=test_request, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "="*60 + "\n")
    
    # Test 4: Get available bots
    print("🔍 Testing get_available_bots method...")
    test_request = {
        "jsonrpc": "2.0",
        "method": "get_available_bots",
        "params": {},
        "id": 3
    }
    
    try:
        response = requests.post(base_url, json=test_request, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "="*60 + "\n")
    
    # Test 5: Download Instagram media (with fake data)
    print("🔍 Testing download_instagram_media method...")
    test_request = {
        "jsonrpc": "2.0",
        "method": "download_instagram_media",
        "params": {
            "url": "https://www.instagram.com/p/test123/",
            "bot_token": "1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZ",
            "chat_id": "123456789"
        },
        "id": 4
    }
    
    try:
        response = requests.post(base_url, json=test_request, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "="*60 + "\n")
    
    # Test 6: Invalid method
    print("🔍 Testing invalid method...")
    test_request = {
        "jsonrpc": "2.0",
        "method": "invalid_method",
        "params": {},
        "id": 5
    }
    
    try:
        response = requests.post(base_url, json=test_request, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("🚀 Starting JSONRPC API Tests")
    print("="*60)
    test_jsonrpc_endpoint()
    print("\n✅ Tests completed!")
