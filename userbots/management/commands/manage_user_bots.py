import asyncio
import os
import glob
import shutil
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from pyrogram import Client
from pyrogram.errors import AuthKeyUnregistered


# API credentials
API_ID = ********
API_HASH = "057f7ec6d84ba875d5fdd64c0d47c925"


class Command(BaseCommand):
    help = "Manage existing Telegram user bot accounts"

    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest='action', help='Available actions')
        
        # List accounts
        list_parser = subparsers.add_parser('list', help='List all accounts with detailed info')
        list_parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed account information'
        )
        
        # Verify account
        verify_parser = subparsers.add_parser('verify', help='Verify account authentication')
        verify_parser.add_argument('account_name', help='Account name to verify')
        
        # Remove account
        remove_parser = subparsers.add_parser('remove', help='Remove an account')
        remove_parser.add_argument('account_name', help='Account name to remove')
        remove_parser.add_argument(
            '--backup',
            action='store_true',
            help='Create backup before removing'
        )
        
        # Backup accounts
        backup_parser = subparsers.add_parser('backup', help='Backup all accounts')
        backup_parser.add_argument(
            '--destination',
            default='./backups',
            help='Backup destination directory'
        )
        
        # Restore accounts
        restore_parser = subparsers.add_parser('restore', help='Restore accounts from backup')
        restore_parser.add_argument('backup_path', help='Path to backup directory')

    def handle(self, *args, **options):
        action = options.get('action')
        
        if not action:
            self.stdout.write(self.style.ERROR("❌ Please specify an action"))
            self.stdout.write("Available actions: list, verify, remove, backup, restore")
            return
        
        if action == 'list':
            asyncio.run(self._list_accounts(options.get('detailed', False)))
        elif action == 'verify':
            account_name = options.get('account_name')
            if not account_name:
                raise CommandError("❌ Account name is required for verify action")
            asyncio.run(self._verify_account(account_name))
        elif action == 'remove':
            account_name = options.get('account_name')
            if not account_name:
                raise CommandError("❌ Account name is required for remove action")
            self._remove_account(account_name, options.get('backup', False))
        elif action == 'backup':
            self._backup_accounts(options.get('destination', './backups'))
        elif action == 'restore':
            backup_path = options.get('backup_path')
            if not backup_path:
                raise CommandError("❌ Backup path is required for restore action")
            self._restore_accounts(backup_path)

    async def _list_accounts(self, detailed=False):
        """List all accounts with optional detailed information"""
        self.stdout.write("\n" + "="*80)
        self.stdout.write("📱 USER BOT ACCOUNTS")
        self.stdout.write("="*80)
        
        session_files = glob.glob("*.session")
        if not session_files:
            self.stdout.write("⚠️ No accounts found")
            return
        
        for session_file in sorted(session_files):
            account_name = session_file.replace(".session", "")
            file_size = os.path.getsize(session_file)
            file_modified = datetime.fromtimestamp(os.path.getmtime(session_file))
            
            if file_size > 0:
                status = "✅ AUTHENTICATED"
                size_info = f"({file_size:,} bytes)"
            else:
                status = "❌ EMPTY"
                size_info = "(0 bytes)"
            
            self.stdout.write(f"\n📱 {account_name}")
            self.stdout.write(f"   Status: {status} {size_info}")
            self.stdout.write(f"   Modified: {file_modified.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if detailed and file_size > 0:
                # Try to get account info
                try:
                    client = Client(account_name, api_id=API_ID, api_hash=API_HASH)
                    await client.connect()
                    
                    try:
                        me = await client.get_me()
                        if me:
                            self.stdout.write(f"   👤 Name: {me.first_name} {me.last_name or ''}")
                            self.stdout.write(f"   📱 Phone: {me.phone_number}")
                            self.stdout.write(f"   🆔 ID: {me.id}")
                            self.stdout.write(f"   👤 Username: @{me.username or 'None'}")
                        else:
                            self.stdout.write("   ⚠️ Could not retrieve account info")
                    except AuthKeyUnregistered:
                        self.stdout.write("   ❌ Session expired or invalid")
                    except Exception as e:
                        self.stdout.write(f"   ⚠️ Error getting info: {e}")
                    
                    await client.disconnect()
                except Exception as e:
                    self.stdout.write(f"   ❌ Connection failed: {e}")
        
        self.stdout.write("\n" + "="*80)
        self.stdout.write(f"📊 Total accounts: {len(session_files)}")
        authenticated = sum(1 for f in session_files if os.path.getsize(f) > 0)
        self.stdout.write(f"✅ Authenticated: {authenticated}")
        self.stdout.write(f"❌ Empty/Invalid: {len(session_files) - authenticated}")
        self.stdout.write("="*80)

    async def _verify_account(self, account_name):
        """Verify if an account is properly authenticated"""
        session_file = f"{account_name}.session"
        
        if not os.path.exists(session_file):
            self.stdout.write(self.style.ERROR(f"❌ Account '{account_name}' not found"))
            return
        
        file_size = os.path.getsize(session_file)
        if file_size == 0:
            self.stdout.write(self.style.WARNING(f"⚠️ Account '{account_name}' has empty session file"))
            return
        
        self.stdout.write(f"🔍 Verifying account: {account_name}")
        
        try:
            client = Client(account_name, api_id=API_ID, api_hash=API_HASH)
            await client.connect()
            
            try:
                me = await client.get_me()
                if me:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"✅ Account '{account_name}' is valid and authenticated\n"
                            f"   👤 Name: {me.first_name} {me.last_name or ''}\n"
                            f"   📱 Phone: {me.phone_number}\n"
                            f"   🆔 ID: {me.id}\n"
                            f"   👤 Username: @{me.username or 'None'}"
                        )
                    )
                else:
                    self.stdout.write(self.style.ERROR(f"❌ Could not retrieve account info for '{account_name}'"))
            
            except AuthKeyUnregistered:
                self.stdout.write(self.style.ERROR(f"❌ Account '{account_name}' session is expired or invalid"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"❌ Error verifying account '{account_name}': {e}"))
            
            await client.disconnect()
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Connection failed for '{account_name}': {e}"))

    def _remove_account(self, account_name, create_backup=False):
        """Remove an account with optional backup"""
        session_file = f"{account_name}.session"
        journal_file = f"{account_name}.session-journal"
        
        if not os.path.exists(session_file):
            self.stdout.write(self.style.ERROR(f"❌ Account '{account_name}' not found"))
            return
        
        # Create backup if requested
        if create_backup:
            backup_dir = f"./backups/{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(backup_dir, exist_ok=True)
            
            shutil.copy2(session_file, backup_dir)
            if os.path.exists(journal_file):
                shutil.copy2(journal_file, backup_dir)
            
            self.stdout.write(f"💾 Backup created: {backup_dir}")
        
        # Confirm removal
        confirm = input(f"⚠️ Are you sure you want to remove account '{account_name}'? (yes/no): ")
        if confirm.lower() != 'yes':
            self.stdout.write("❌ Removal cancelled")
            return
        
        # Remove files
        try:
            os.remove(session_file)
            self.stdout.write(f"🗑️ Removed: {session_file}")
            
            if os.path.exists(journal_file):
                os.remove(journal_file)
                self.stdout.write(f"🗑️ Removed: {journal_file}")
            
            self.stdout.write(self.style.SUCCESS(f"✅ Account '{account_name}' removed successfully"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error removing account: {e}"))

    def _backup_accounts(self, destination):
        """Backup all account session files"""
        session_files = glob.glob("*.session")
        journal_files = glob.glob("*.session-journal")
        
        if not session_files:
            self.stdout.write(self.style.WARNING("⚠️ No accounts to backup"))
            return
        
        # Create backup directory with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = os.path.join(destination, f"userbot_backup_{timestamp}")
        os.makedirs(backup_dir, exist_ok=True)
        
        # Copy session files
        backed_up = 0
        for session_file in session_files:
            try:
                shutil.copy2(session_file, backup_dir)
                backed_up += 1
                self.stdout.write(f"💾 Backed up: {session_file}")
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"❌ Failed to backup {session_file}: {e}"))
        
        # Copy journal files
        for journal_file in journal_files:
            try:
                shutil.copy2(journal_file, backup_dir)
                self.stdout.write(f"💾 Backed up: {journal_file}")
            except Exception as e:
                self.stdout.write(self.style.WARNING(f"⚠️ Failed to backup {journal_file}: {e}"))
        
        self.stdout.write(
            self.style.SUCCESS(
                f"✅ Backup completed: {backup_dir}\n"
                f"📊 {backed_up} accounts backed up"
            )
        )

    def _restore_accounts(self, backup_path):
        """Restore accounts from backup"""
        if not os.path.exists(backup_path):
            self.stdout.write(self.style.ERROR(f"❌ Backup path not found: {backup_path}"))
            return
        
        session_files = glob.glob(os.path.join(backup_path, "*.session"))
        if not session_files:
            self.stdout.write(self.style.ERROR(f"❌ No session files found in backup: {backup_path}"))
            return
        
        self.stdout.write(f"🔍 Found {len(session_files)} accounts in backup")
        
        # Confirm restore
        confirm = input("⚠️ This will overwrite existing accounts. Continue? (yes/no): ")
        if confirm.lower() != 'yes':
            self.stdout.write("❌ Restore cancelled")
            return
        
        # Restore files
        restored = 0
        for session_file in session_files:
            try:
                filename = os.path.basename(session_file)
                shutil.copy2(session_file, filename)
                restored += 1
                self.stdout.write(f"📥 Restored: {filename}")
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"❌ Failed to restore {session_file}: {e}"))
        
        # Restore journal files
        journal_files = glob.glob(os.path.join(backup_path, "*.session-journal"))
        for journal_file in journal_files:
            try:
                filename = os.path.basename(journal_file)
                shutil.copy2(journal_file, filename)
                self.stdout.write(f"📥 Restored: {filename}")
            except Exception as e:
                self.stdout.write(self.style.WARNING(f"⚠️ Failed to restore {journal_file}: {e}"))
        
        self.stdout.write(
            self.style.SUCCESS(
                f"✅ Restore completed\n"
                f"📊 {restored} accounts restored"
            )
        )
