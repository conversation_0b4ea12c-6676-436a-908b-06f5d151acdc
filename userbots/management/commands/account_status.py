import glob
import os
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Show status of all discovered Telegram accounts"

    def handle(self, *args, **options):
        # Suppress unused parameter warnings
        _ = args, options
        
        self.stdout.write(self.style.SUCCESS("🔍 Discovering Telegram accounts..."))
        
        # Discover session files
        session_files = glob.glob("*.session")
        
        if not session_files:
            self.stdout.write(self.style.WARNING("⚠️ No session files found!"))
            return
        
        self.stdout.write(f"\n📱 Found {len(session_files)} account(s):")
        self.stdout.write("=" * 60)
        
        for session_file in sorted(session_files):
            account_name = session_file.replace(".session", "")
            file_size = os.path.getsize(session_file)
            
            if file_size > 0:
                status = "✅ AUTHENTICATED"
                size_info = f"({file_size:,} bytes)"
            else:
                status = "❌ EMPTY/NOT AUTHENTICATED"
                size_info = "(0 bytes)"
            
            self.stdout.write(f"📱 {account_name:<20} {status} {size_info}")
        
        self.stdout.write("=" * 60)
        
        # Show configuration
        self.stdout.write("\n⚙️ Configuration:")
        self.stdout.write(f"   • Task cooldown: 5 minutes")
        self.stdout.write(f"   • Max tasks per cooldown: 2")
        self.stdout.write(f"   • Queue check interval: 15 seconds")
        
        # Show usage
        self.stdout.write("\n🚀 Usage:")
        self.stdout.write("   python manage.py run_user_bots")

        # Show management commands
        self.stdout.write("\n🛠️ Management Commands:")
        self.stdout.write("   python manage.py add_user_bot <account_name>     # Add new account")
        self.stdout.write("   python manage.py manage_user_bots list           # List all accounts")
        self.stdout.write("   python manage.py manage_user_bots verify <name>  # Verify account")
        self.stdout.write("   python manage.py manage_user_bots remove <name>  # Remove account")
        self.stdout.write("   python manage.py manage_user_bots backup         # Backup accounts")

        self.stdout.write("\n📝 Notes:")
        self.stdout.write("   • Only authenticated accounts will be able to connect")
        self.stdout.write("   • Empty session files are for testing purposes only")
        self.stdout.write("   • Use 'add_user_bot' command to authenticate new accounts")
        self.stdout.write("   • Keep session files secure and private")
