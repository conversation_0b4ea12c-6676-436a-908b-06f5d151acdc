import asyncio
import json
import requests
import glob
import time
from typing import Dict, Optional

from pyrogram import Client, filters
from pyrogram.handlers import <PERSON><PERSON><PERSON><PERSON>
from django.core.management.base import BaseCommand
from django.core.cache import cache
from django_redis import get_redis_connection
from bots.utils import TelegramBotUtils


API_ID = ********
API_HASH = "057f7ec6d84ba875d5fdd64c0d47c925"

# Configuration constants
TASK_COOLDOWN_MINUTES = 5
MAX_TASKS_PER_COOLDOWN = 2
QUEUE_CHECK_INTERVAL = 15  # seconds

redis_client = get_redis_connection("default")


class AccountManager:
    """Manages multiple Telegram accounts with task distribution and cooldown logic"""

    def __init__(self):
        self.accounts: Dict[str, Dict] = {}
        self.account_clients: Dict[str, Client] = {}
        self.discover_accounts()

    def discover_accounts(self):
        """Discover all available account session files"""
        session_files = glob.glob("*.session")
        for session_file in session_files:
            account_name = session_file.replace(".session", "")
            self.accounts[account_name] = {
                "last_tasks": [],  # List of task timestamps
                "is_busy": False,
                "current_task": None
            }
            print(f"🔍 Discovered account: {account_name}")

    def get_available_account(self) -> Optional[str]:
        """Get an account that can handle a new task"""
        current_time = time.time()

        for account_name, account_info in self.accounts.items():
            # Skip if account is currently busy
            if account_info["is_busy"]:
                continue

            # Clean old tasks (older than 5 minutes)
            cutoff_time = current_time - (TASK_COOLDOWN_MINUTES * 60)
            account_info["last_tasks"] = [
                task_time for task_time in account_info["last_tasks"]
                if task_time > cutoff_time
            ]

            # Check if account can take more tasks
            if len(account_info["last_tasks"]) < MAX_TASKS_PER_COOLDOWN:
                return account_name

        return None

    def mark_account_busy(self, account_name: str, task_data: dict):
        """Mark account as busy with a task"""
        if account_name in self.accounts:
            self.accounts[account_name]["is_busy"] = True
            self.accounts[account_name]["current_task"] = task_data
            self.accounts[account_name]["last_tasks"].append(time.time())

    def mark_account_free(self, account_name: str):
        """Mark account as free"""
        if account_name in self.accounts:
            self.accounts[account_name]["is_busy"] = False
            self.accounts[account_name]["current_task"] = None

    async def get_client(self, account_name: str) -> Optional[Client]:
        """Get or create a client for the account"""
        if account_name not in self.account_clients:
            try:
                client = Client(account_name, api_id=API_ID, api_hash=API_HASH)
                await client.start()
                self.account_clients[account_name] = client
                print(f"✅ Started client for account: {account_name}")
            except Exception as e:
                print(f"❌ Failed to start client for {account_name}: {e}")
                return None

        return self.account_clients.get(account_name)


# Global account manager instance
account_manager = AccountManager()


# ---------------------- Utility Functions ----------------------
def print_account_status():
    """Print current status of all accounts"""
    print("\n" + "="*50)
    print("📊 ACCOUNT STATUS")
    print("="*50)
    current_time = time.time()

    for account_name, account_info in account_manager.accounts.items():
        status = "🔴 BUSY" if account_info["is_busy"] else "🟢 FREE"

        # Clean old tasks
        cutoff_time = current_time - (TASK_COOLDOWN_MINUTES * 60)
        recent_tasks = [
            task_time for task_time in account_info["last_tasks"]
            if task_time > cutoff_time
        ]

        tasks_count = len(recent_tasks)
        max_tasks = MAX_TASKS_PER_COOLDOWN

        print(f"📱 {account_name}: {status} | Tasks: {tasks_count}/{max_tasks}")

        if account_info["current_task"]:
            task = account_info["current_task"]
            print(f"   🔄 Current: {task.get('url', 'N/A')[:50]}...")

        if recent_tasks:
            next_available = max(recent_tasks) + (TASK_COOLDOWN_MINUTES * 60)
            wait_time = max(0, next_available - current_time)
            if wait_time > 0:
                print(f"   ⏰ Next available in: {wait_time/60:.1f} minutes")

    print("="*50 + "\n")


async def status_monitor():
    """Periodically print account status"""
    while True:
        await asyncio.sleep(60)  # Print status every minute
        print_account_status()


# ---------------------- 1. Queue bilan ishlash ----------------------
async def process_queue():
    """Process download queue with multiple accounts"""
    while True:
        item = redis_client.rpop("download_queue")
        if item:
            try:
                task_data = json.loads(item)
                url = task_data.get("url")

                if not url:
                    print("⚠️ No URL in task data, skipping")
                    continue

                # Get available account
                account_name = account_manager.get_available_account()
                if not account_name:
                    # No available accounts, put task back in queue
                    redis_client.lpush("download_queue", item)
                    print("⏳ No available accounts, waiting...")
                    await asyncio.sleep(30)  # Wait longer when no accounts available
                    continue

                # Get client for the account
                client = await account_manager.get_client(account_name)
                if not client:
                    print(f"❌ Failed to get client for {account_name}")
                    redis_client.lpush("download_queue", item)  # Put task back
                    continue

                # Mark account as busy
                account_manager.mark_account_busy(account_name, task_data)

                try:
                    # Send message to YuklaydiBot
                    msg = await client.send_message("@YuklaydiBot", url)
                    if msg:
                        # Add account info to task data for tracking
                        task_data["account_name"] = account_name
                        redis_client.rpush("media_pending_queue", json.dumps(task_data))
                        print(f"📤 [{account_name}] Sent to @YuklaydiBot | msg_id: {msg.id}")
                    else:
                        print(f"❌ [{account_name}] Failed to send message")
                        redis_client.lpush("download_queue", item)  # Put task back
                finally:
                    # Mark account as free after task completion
                    account_manager.mark_account_free(account_name)

            except Exception as e:
                print(f"❌ Error processing queue: {e}")
                # Mark account as free in case of error
                if 'account_name' in locals():
                    account_manager.mark_account_free(account_name)

        await asyncio.sleep(QUEUE_CHECK_INTERVAL)

# ---------------------- 2. Media qabul qilish va qayta yuborish ----------------------
async def media_forwarder(client, message):
    """Handle incoming media from YuklaydiBot and forward to target bot"""
    try:
        if not message.chat or message.chat.username != "YuklaydiBot":
            return

        # Get the account name from the client
        account_name = None
        for name, acc_client in account_manager.account_clients.items():
            if acc_client == client:
                account_name = name
                break

        if not account_name:
            print("⚠️ Could not identify account for incoming media")
            return

        task_json = redis_client.lpop("media_pending_queue")
        if not task_json:
            print(f"⚠️ [{account_name}] No pending task found for incoming media.")
            task_json = cache.get("media_pending_queue")
            print(f"[{account_name}] media_pending_queue from cache: {task_json}")
            if not task_json:
                print(f"❌ [{account_name}] No pending task found in cache either.")
                return
        else:
            cache.set("media_pending_queue", task_json, timeout=60)

        task_data = json.loads(task_json)
        bot_token = task_data.get("bot_token")
        chat_id = task_data.get("chat_id")
        bot_username = task_data.get("bot_username")
        task_account = task_data.get("account_name", "unknown")

        # Auto-fetch bot_username if missing
        if not bot_username and bot_token:
            try:
                print(f"🔍 [{account_name}] Bot username missing, fetching from token...")
                bot_username = TelegramBotUtils.get_bot_username(bot_token)
                if bot_username:
                    task_data["bot_username"] = bot_username
                    print(f"✅ [{account_name}] Auto-fetched bot username: {bot_username}")
                else:
                    print(f"❌ [{account_name}] Could not fetch bot username from token")
            except Exception as e:
                print(f"⚠️ [{account_name}] Error fetching bot username: {e}")

        if not all([bot_token, chat_id, bot_username]):
            print(f"❌ [{account_name}] Missing required fields in task data.")
            return

        # Verify this media belongs to this account's task
        if task_account != account_name:
            print(f"⚠️ [{account_name}] Media doesn't match account task (expected: {task_account})")
            # Put the task back for the correct account
            redis_client.lpush("media_pending_queue", task_json)
            return

        # 1. copy_message orqali media yuborish
        fwd_msg = await client.copy_message(
            chat_id=bot_username,
            from_chat_id=message.chat.id,
            message_id=message.id
        )

        if not fwd_msg:
            print("❌ Failed to copy message.")
            return

        media = None
        method = None
        media_field = None

        if getattr(fwd_msg, "photo", None):
            media = fwd_msg.photo.file_id
            method = "sendPhoto"
            media_field = "photo"
        elif getattr(fwd_msg, "video", None):
            media = fwd_msg.video.file_id
            method = "sendVideo"
            media_field = "video"
        elif getattr(fwd_msg, "document", None):
            media = fwd_msg.document.file_id
            method = "sendDocument"
            media_field = "document"
        elif getattr(fwd_msg, "animation", None):
            media = fwd_msg.animation.file_id
            method = "sendAnimation"
            media_field = "animation"
        else:
            print("❌ Unsupported or missing media after copy.")
            return

        print(f"📦 [{account_name}] Forwarded {media_field} | file_id: {media} | sending to chat_id: {chat_id}")

        # 3. Telegram Bot API orqali yuborish
        url = f"https://api.telegram.org/bot{bot_token}/{method}"
        resp = requests.post(url, data={
            "chat_id": chat_id,
            media_field: media
        })

        if resp.status_code == 200:
            print(f"✅ [{account_name}] {media_field} sent to chat_id {chat_id}")
        else:
            print(f"❌ [{account_name}] Failed to send {media_field}: {resp.text}")

        # 4. Agar queue bo‘sh bo‘lsa — cache'ga qaytarish
        if not redis_client.lindex("media_pending_queue", 0) and not cache.get("media_pending_queue"):
            print(f"ℹ️ [{account_name}] Queue is empty. Restoring task to cache.")
            cache.set("media_pending_queue", json.dumps(task_data), timeout=60)

    except Exception as e:
        print(f"❌ Error in media_forwarder: {e}")


class Command(BaseCommand):
    help = "Run multiple user bots that process media replies from @YuklaydiBot"

    def handle(self, *args, **options):
        # Suppress unused parameter warnings
        _ = args, options
        self.stdout.write(self.style.SUCCESS("Starting Telegram userbots..."))

        async def main():
            # Start all available accounts
            tasks = []

            # Start the queue processor
            tasks.append(asyncio.create_task(process_queue()))

            # Start the status monitor
            tasks.append(asyncio.create_task(status_monitor()))

            # Start clients for all discovered accounts
            for account_name in account_manager.accounts.keys():
                try:
                    client = await account_manager.get_client(account_name)
                    if client:
                        # Add media handler for this client
                        client.add_handler(
                            MessageHandler(
                                media_forwarder,
                                filters.photo | filters.video | filters.document | filters.animation
                            )
                        )
                        self.stdout.write(
                            self.style.SUCCESS(f"✅ Account {account_name} is now listening for media replies...")
                        )
                    else:
                        self.stdout.write(
                            self.style.WARNING(f"⚠️ Failed to start account {account_name}")
                        )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"❌ Error starting account {account_name}: {e}")
                    )

            if not account_manager.account_clients:
                self.stdout.write(self.style.ERROR("❌ No accounts could be started!"))
                return

            self.stdout.write(
                self.style.SUCCESS(
                    f"🚀 Started {len(account_manager.account_clients)} accounts. "
                    f"Queue processor is running..."
                )
            )

            try:
                # Wait indefinitely
                await asyncio.Event().wait()
            finally:
                # Clean shutdown
                self.stdout.write(self.style.WARNING("🛑 Shutting down..."))
                for account_name, client in account_manager.account_clients.items():
                    try:
                        await client.stop()
                        self.stdout.write(self.style.SUCCESS(f"✅ Stopped {account_name}"))
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"❌ Error stopping {account_name}: {e}"))

        try:
            asyncio.run(main())
        except KeyboardInterrupt:
            self.stdout.write(self.style.WARNING("🛑 Received interrupt signal"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"💥 Bot crashed: {e}"))
