import asyncio
import os
import glob
import re
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from pyrogram import Client
from pyrogram.errors import (
    SessionPasswordNeeded,
    PhoneCodeInvalid,
    PhoneNumberInvalid,
    FloodWait,
    AuthKeyUnregistered
)


# API credentials will be obtained from user input


class Command(BaseCommand):
    help = "Add a new Telegram user bot account to the system with auto-generated names"

    def _generate_account_name(self, phone_number):
        """Generate account name from phone number and current date"""
        # Extract only digits from phone number
        phone_digits = re.sub(r'\D', '', phone_number)

        # Get current date in DDMMYYYY format
        current_date = datetime.now().strftime('%d%m%Y')

        # Create account name: phone_date.session
        account_name = f"{phone_digits}_{current_date}"

        return account_name

    def add_arguments(self, parser):
        parser.add_argument(
            'phone',
            type=str,
            help='Phone number for the account (with country code, e.g., +************)'
        )
        parser.add_argument(
            '--account-name',
            type=str,
            help='Custom name for the account (if not provided, auto-generated from phone and date)'
        )
        parser.add_argument(
            '--list-existing',
            action='store_true',
            help='List all existing accounts before adding new one'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force overwrite existing account session'
        )

    def handle(self, *args, **options):
        phone_number = options['phone']
        custom_account_name = options.get('account_name')
        list_existing = options.get('list_existing', False)
        force_overwrite = options.get('force', False)

        # Generate account name automatically if not provided
        if custom_account_name:
            account_name = custom_account_name
            self.stdout.write(f"📝 Using custom account name: {account_name}")
        else:
            account_name = self._generate_account_name(phone_number)
            self.stdout.write(f"🤖 Auto-generated account name: {account_name}")
            self.stdout.write(f"📱 Phone: {phone_number} → Date: {datetime.now().strftime('%d%m%Y')}")

        # List existing accounts if requested
        if list_existing:
            self._list_existing_accounts()

        # Validate account name
        if not self._validate_account_name(account_name):
            return

        # Check if account already exists
        session_file = f"{account_name}.session"
        if os.path.exists(session_file) and not force_overwrite:
            raise CommandError(
                f"❌ Account '{account_name}' already exists! "
                f"Use --force to overwrite or choose a different name."
            )

        # Get API credentials from user input
        self.stdout.write("\n" + "="*60)
        self.stdout.write("🔑 API CREDENTIALS REQUIRED")
        self.stdout.write("="*60)
        self.stdout.write("You need to provide your Telegram API credentials.")
        self.stdout.write("Get them from: https://my.telegram.org/apps")
        self.stdout.write("="*60)

        api_id = input("📋 Enter your API_ID: ").strip()
        api_hash = input("🔐 Enter your API_HASH: ").strip()

        # Validate API credentials
        if not api_id or not api_hash:
            self.stdout.write(
                self.style.ERROR('❌ API_ID and API_HASH are required')
            )
            return

        try:
            api_id = int(api_id)
        except ValueError:
            self.stdout.write(
                self.style.ERROR('❌ API_ID must be a valid integer')
            )
            return

        # Start authentication process
        self.stdout.write(
            self.style.SUCCESS(f"🚀 Starting authentication for account: {account_name}")
        )

        try:
            asyncio.run(self._authenticate_account(account_name, phone_number, api_id, api_hash))
        except KeyboardInterrupt:
            self.stdout.write(self.style.WARNING("🛑 Authentication cancelled by user"))
            # Clean up partial session file if it exists
            session_file = f"{account_name}.session"
            if os.path.exists(session_file):
                os.remove(session_file)
                self.stdout.write(f"🧹 Cleaned up partial session file: {session_file}")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"💥 Authentication failed: {e}"))
            # Clean up partial session file if it exists
            session_file = f"{account_name}.session"
            if os.path.exists(session_file):
                os.remove(session_file)
                self.stdout.write(f"🧹 Cleaned up partial session file: {session_file}")

    def _list_existing_accounts(self):
        """List all existing account session files"""
        self.stdout.write("\n" + "="*60)
        self.stdout.write("📱 EXISTING ACCOUNTS")
        self.stdout.write("="*60)
        
        session_files = glob.glob("*.session")
        if not session_files:
            self.stdout.write("⚠️ No existing accounts found")
        else:
            for session_file in sorted(session_files):
                account_name = session_file.replace(".session", "")
                file_size = os.path.getsize(session_file)
                
                if file_size > 0:
                    status = "✅ AUTHENTICATED"
                    size_info = f"({file_size:,} bytes)"
                else:
                    status = "❌ EMPTY"
                    size_info = "(0 bytes)"
                
                self.stdout.write(f"📱 {account_name:<20} {status} {size_info}")
        
        self.stdout.write("="*60 + "\n")

    def _validate_account_name(self, account_name):
        """Validate the account name"""
        if not account_name:
            self.stdout.write(self.style.ERROR("❌ Account name cannot be empty"))
            return False
        
        if not account_name.replace('_', '').replace('-', '').isalnum():
            self.stdout.write(
                self.style.ERROR(
                    "❌ Account name can only contain letters, numbers, underscores, and hyphens"
                )
            )
            return False
        
        if len(account_name) > 50:
            self.stdout.write(self.style.ERROR("❌ Account name too long (max 50 characters)"))
            return False
        
        return True

    async def _authenticate_account(self, account_name, phone_number, api_id, api_hash):
        """Authenticate a new Telegram account"""
        client = None

        try:
            # Create Pyrogram client
            client = Client(
                account_name,
                api_id=api_id,
                api_hash=api_hash,
                phone_number=phone_number
            )

            self.stdout.write("🔗 Connecting to Telegram...")
            
            # Connect to Telegram
            await client.connect()
            
            # Check if already authorized
            if await client.get_me():
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Account '{account_name}' is already authenticated!")
                )
                return

        except AuthKeyUnregistered:
            # Account needs authentication
            pass
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Connection failed: {e}"))
            return

        try:
            # Start authentication process
            if not phone_number:
                phone_number = input("📱 Enter phone number (with country code, e.g., +**********): ")
            
            self.stdout.write(f"📞 Sending code to {phone_number}...")
            
            # Send code
            sent_code = await client.send_code(phone_number)
            
            # Get verification code from user
            code = input("🔐 Enter the verification code you received: ")
            
            try:
                # Sign in with code
                await client.sign_in(phone_number, sent_code.phone_code_hash, code)
                
            except SessionPasswordNeeded:
                # Two-factor authentication is enabled
                self.stdout.write("🔒 Two-factor authentication detected")
                password = input("🔑 Enter your 2FA password: ")
                await client.check_password(password)
            
            except PhoneCodeInvalid:
                self.stdout.write(self.style.ERROR("❌ Invalid verification code"))
                return
            
            except PhoneNumberInvalid:
                self.stdout.write(self.style.ERROR("❌ Invalid phone number"))
                return

            # Verify authentication was successful
            me = await client.get_me()
            if me:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"✅ Successfully authenticated account: {account_name}\n"
                        f"   👤 Name: {me.first_name} {me.last_name or ''}\n"
                        f"   📱 Phone: {me.phone_number}\n"
                        f"   🆔 ID: {me.id}"
                    )
                )
                
                # Show next steps
                self._show_next_steps(account_name)
            else:
                self.stdout.write(self.style.ERROR("❌ Authentication verification failed"))

        except FloodWait as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Rate limited by Telegram. Wait {e.value} seconds and try again.")
            )
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Authentication error: {e}"))
        
        finally:
            if client:
                await client.disconnect()

    def _show_next_steps(self, account_name):
        """Show next steps after successful authentication"""
        self.stdout.write("\n" + "="*60)
        self.stdout.write("🎉 AUTHENTICATION SUCCESSFUL!")
        self.stdout.write("="*60)
        self.stdout.write("📋 Next steps:")
        self.stdout.write(f"   1. Your account '{account_name}' is now ready to use")
        self.stdout.write("   2. Check account status:")
        self.stdout.write("      python manage.py account_status")
        self.stdout.write("   3. Start the multi-account system:")
        self.stdout.write("      python manage.py run_user_bots")
        self.stdout.write("   4. The new account will be automatically discovered and used")
        self.stdout.write("\n💡 Usage Examples:")
        self.stdout.write("   • Add account with auto-generated name:")
        self.stdout.write("     python manage.py add_user_bot +************")
        self.stdout.write("   • Add account with custom name:")
        self.stdout.write("     python manage.py add_user_bot +************ --account-name my_custom_bot")
        self.stdout.write("\n💡 Tips:")
        self.stdout.write("   • Account names are auto-generated as: phone_DDMMYYYY")
        self.stdout.write("   • Keep your session file secure")
        self.stdout.write("   • Don't share session files with others")
        self.stdout.write("   • The account will respect rate limits (5min cooldown, 2 tasks max)")
        self.stdout.write("="*60)
