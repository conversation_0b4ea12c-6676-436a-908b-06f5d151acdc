"""
Custom middleware for X-AUTH header authentication.
"""
import logging
from django.http import JsonResponse
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)


class XAuthMiddleware(MiddlewareMixin):
    """
    Middleware to check X-AUTH header for every request.
    
    This middleware validates the X-AUTH header against a configured
    authentication token. If the header is missing or invalid,
    it returns a 401 Unauthorized response.
    """

    EXEMPT_URLS = [
        '/admin/',
        '/health/',
        '/ping/',
    ]
    
    def process_request(self, request):
        """
        Process incoming request to check X-AUTH header.
        
        Args:
            request: Django HttpRequest object
            
        Returns:
            JsonResponse with 401 status if authentication fails,
            None if authentication passes
        """
        if self._is_exempt_url(request.path):
            return None

        x_auth_header = request.META.get('HTTP_X_AUTH')
        
        if not x_auth_header:
            logger.warning(f"Missing X-AUTH header for request to {request.path}")
            return JsonResponse(
                {'error': 'X-AUTH header is required'}, 
                status=401
            )

        expected_token = getattr(settings, 'X_AUTH_TOKEN', None)

        if not expected_token:
            logger.error("X_AUTH_TOKEN not configured in settings")
            return JsonResponse(
                {'error': 'Authentication not properly configured'}, 
                status=500
            )

        if x_auth_header != expected_token:
            logger.warning(f"Invalid X-AUTH token for request to {request.path}")
            return JsonResponse(
                {'error': 'Invalid X-AUTH token'}, 
                status=401
            )

        logger.debug(f"X-AUTH authentication successful for {request.path}")
        return None

    def _is_exempt_url(self, path):
        """
        Check if the URL path is exempt from authentication.
        
        Args:
            path: URL path string
            
        Returns:
            bool: True if URL is exempt, False otherwise
        """
        for exempt_url in self.EXEMPT_URLS:
            if path.startswith(exempt_url):
                return True
        return False
