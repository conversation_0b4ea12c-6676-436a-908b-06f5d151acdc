import asyncio
import json
from rest_framework import views
from rest_framework.response import Response

from instagram.serializers import DownloadSerializer

from django_redis import get_redis_connection  # Qo'shildi


class DownloadView(views.APIView):
    serializer_class = DownloadSerializer
    authentication_classes = []
    permission_classes = []

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        redis_client = get_redis_connection("default")
       
        redis_client.lpush("download_queue", json.dumps({
            "url": serializer.validated_data.get("url", ""),
            "bot_username": serializer.validated_data.get("bot_username", ""),
            "hosting": "instagram",
            "bot_token": serializer.validated_data.get("bot_token", ""),
            "chat_id": serializer.validated_data.get("chat_id", "")
        }))

        return Response()
