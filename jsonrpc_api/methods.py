"""
JSONRPC methods for the SaverAPI project.
"""
import json
import asyncio
from typing import Dict, Any, Optional
from django_redis import get_redis_connection
from bots.utils import TelegramBotUtils
from bots.service import BotsService
from bots.models import TargetBots


class JSONRPCMethods:
    """
    Collection of JSONRPC methods for the SaverAPI.
    """
    
    @staticmethod
    def download_instagram_media(url: str, bot_token: str, chat_id: str, bot_username: Optional[str] = None) -> Dict[str, Any]:
        """
        Download Instagram media using the existing queue system.
        
        Args:
            url: Instagram URL to download
            bot_token: Telegram bot token
            chat_id: Telegram chat ID
            bot_username: Optional bot username (will be fetched if not provided)
            
        Returns:
            Dict with success status and message
        """
        try:
            # Validate inputs
            if not url or not bot_token or not chat_id:
                return {
                    "success": False,
                    "error": "Missing required parameters: url, bot_token, chat_id"
                }
            
            # Get bot username if not provided
            if not bot_username:
                bot_username = TelegramBotUtils.get_bot_username(bot_token)
                if not bot_username:
                    return {
                        "success": False,
                        "error": "Could not retrieve bot username from token"
                    }
            
            # Add to Redis queue
            redis_client = get_redis_connection("default")
            queue_data = {
                "url": url,
                "bot_username": bot_username,
                "hosting": "instagram",
                "bot_token": bot_token,
                "chat_id": chat_id
            }
            
            redis_client.lpush("download_queue", json.dumps(queue_data))
            
            return {
                "success": True,
                "message": "Download request added to queue successfully",
                "data": {
                    "url": url,
                    "bot_username": bot_username,
                    "chat_id": chat_id
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to process download request: {str(e)}"
            }
    
    @staticmethod
    def get_bot_info(bot_token: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        Get bot information from Telegram API.
        
        Args:
            bot_token: Telegram bot token
            use_cache: Whether to use cache for bot info
            
        Returns:
            Dict with bot information or error
        """
        try:
            if not bot_token:
                return {
                    "success": False,
                    "error": "Bot token is required"
                }
            
            bot_details = TelegramBotUtils.get_bot_details(bot_token, use_cache)
            
            if not bot_details:
                return {
                    "success": False,
                    "error": "Could not retrieve bot information"
                }
            
            return {
                "success": True,
                "data": bot_details
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to get bot info: {str(e)}"
            }
    
    @staticmethod
    def validate_bot_token(bot_token: str) -> Dict[str, Any]:
        """
        Validate a Telegram bot token.
        
        Args:
            bot_token: Telegram bot token to validate
            
        Returns:
            Dict with validation result
        """
        try:
            if not bot_token:
                return {
                    "success": False,
                    "error": "Bot token is required"
                }
            
            is_valid = TelegramBotUtils.validate_bot_token(bot_token)
            
            return {
                "success": True,
                "data": {
                    "is_valid": is_valid,
                    "bot_token": bot_token[:20] + "..." if len(bot_token) > 20 else bot_token
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to validate bot token: {str(e)}"
            }
    
    @staticmethod
    async def get_available_bots() -> Dict[str, Any]:
        """
        Get list of available bots from the database.
        
        Returns:
            Dict with list of available bots
        """
        try:
            bots = await BotsService.get_available_bots()
            
            bots_data = []
            for bot in bots:
                bots_data.append({
                    "id": bot.id,
                    "name": bot.name,
                    "username": bot.username,
                    "is_active": bot.is_active,
                    "can_dl_from_insta": bot.can_dl_from_insta,
                    "can_dl_from_tiktok": bot.can_dl_from_tiktok,
                    "can_dl_from_youtube": bot.can_dl_from_youtube,
                    "is_queued": bot.is_queued
                })
            
            return {
                "success": True,
                "data": {
                    "bots": bots_data,
                    "count": len(bots_data)
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to get available bots: {str(e)}"
            }
    
    @staticmethod
    async def get_bot_by_username(username: str) -> Dict[str, Any]:
        """
        Get bot information by username.
        
        Args:
            username: Bot username to search for
            
        Returns:
            Dict with bot information or error
        """
        try:
            if not username:
                return {
                    "success": False,
                    "error": "Username is required"
                }
            
            bot = await BotsService.get_bot_by_username(username)
            
            if not bot:
                return {
                    "success": False,
                    "error": f"Bot with username '{username}' not found"
                }
            
            return {
                "success": True,
                "data": {
                    "id": bot.id,
                    "name": bot.name,
                    "username": bot.username,
                    "is_active": bot.is_active,
                    "can_dl_from_insta": bot.can_dl_from_insta,
                    "can_dl_from_tiktok": bot.can_dl_from_tiktok,
                    "can_dl_from_youtube": bot.can_dl_from_youtube,
                    "is_queued": bot.is_queued
                }
            }
            
        except TargetBots.DoesNotExist:
            return {
                "success": False,
                "error": f"Bot with username '{username}' not found"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to get bot by username: {str(e)}"
            }
    
    @staticmethod
    def get_queue_status() -> Dict[str, Any]:
        """
        Get current queue status from Redis.
        
        Returns:
            Dict with queue information
        """
        try:
            redis_client = get_redis_connection("default")
            queue_length = redis_client.llen("download_queue")
            
            return {
                "success": True,
                "data": {
                    "queue_length": queue_length,
                    "queue_name": "download_queue"
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to get queue status: {str(e)}"
            }
