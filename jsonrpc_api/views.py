"""
JSONRPC views for the SaverAPI project.
"""
import json
import asyncio
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from jsonrpcserver import method, dispatch, async_dispatch, Result, Success, Error
from asgiref.sync import sync_to_async
from .methods import JSONRPCMethods


# Register JSONRPC methods
@method
def download_instagram_media(url: str, bot_token: str, chat_id: str, bot_username: str = None) -> dict:
    """Download Instagram media."""
    return JSONRPCMethods.download_instagram_media(url, bot_token, chat_id, bot_username)


@method
def get_bot_info(bot_token: str, use_cache: bool = True) -> dict:
    """Get bot information from Telegram API."""
    return JSONRPCMethods.get_bot_info(bot_token, use_cache)


@method
def validate_bot_token(bot_token: str) -> dict:
    """Validate a Telegram bot token."""
    return JSONRPCMethods.validate_bot_token(bot_token)


@method
async def get_available_bots() -> dict:
    """Get list of available bots."""
    return await JSONRPCMethods.get_available_bots()


@method
async def get_bot_by_username(username: str) -> dict:
    """Get bot information by username."""
    return await JSONRPCMethods.get_bot_by_username(username)


@method
def get_queue_status() -> dict:
    """Get current queue status."""
    return JSONRPCMethods.get_queue_status()


@method_decorator(csrf_exempt, name='dispatch')
class JSONRPCView(View):
    """
    Main JSONRPC endpoint view.
    """

    def post(self, request):
        """
        Handle JSONRPC POST requests.
        """
        try:
            # Parse request body
            request_data = json.loads(request.body.decode('utf-8'))

            # Check if method requires async execution
            method_name = request_data.get('method', '')
            async_methods = ['get_available_bots', 'get_bot_by_username']

            if method_name in async_methods:
                # Use async dispatch for async methods
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    response = loop.run_until_complete(async_dispatch(request_data))
                finally:
                    loop.close()
            else:
                # Use sync dispatch for sync methods
                response = dispatch(request_data)

            # Return JSON response
            return JsonResponse(response, safe=False)

        except json.JSONDecodeError:
            return JsonResponse({
                "jsonrpc": "2.0",
                "error": {
                    "code": -32700,
                    "message": "Parse error"
                },
                "id": None
            }, status=400)
        except Exception as e:
            return JsonResponse({
                "jsonrpc": "2.0",
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                },
                "id": None
            }, status=500)
    
    def get(self, request):
        """
        Handle GET requests - return API documentation.
        """
        documentation = {
            "jsonrpc": "2.0",
            "info": {
                "title": "SaverAPI JSONRPC",
                "version": "1.0.0",
                "description": "JSONRPC API for SaverAPI project"
            },
            "methods": [
                {
                    "name": "download_instagram_media",
                    "description": "Download Instagram media using the queue system",
                    "params": {
                        "url": {"type": "string", "required": True, "description": "Instagram URL to download"},
                        "bot_token": {"type": "string", "required": True, "description": "Telegram bot token"},
                        "chat_id": {"type": "string", "required": True, "description": "Telegram chat ID"},
                        "bot_username": {"type": "string", "required": False, "description": "Bot username (optional)"}
                    }
                },
                {
                    "name": "get_bot_info",
                    "description": "Get bot information from Telegram API",
                    "params": {
                        "bot_token": {"type": "string", "required": True, "description": "Telegram bot token"},
                        "use_cache": {"type": "boolean", "required": False, "description": "Use cache for bot info (default: true)"}
                    }
                },
                {
                    "name": "validate_bot_token",
                    "description": "Validate a Telegram bot token",
                    "params": {
                        "bot_token": {"type": "string", "required": True, "description": "Telegram bot token to validate"}
                    }
                },
                {
                    "name": "get_available_bots",
                    "description": "Get list of available bots from database",
                    "params": {}
                },
                {
                    "name": "get_bot_by_username",
                    "description": "Get bot information by username",
                    "params": {
                        "username": {"type": "string", "required": True, "description": "Bot username to search for"}
                    }
                },
                {
                    "name": "get_queue_status",
                    "description": "Get current queue status from Redis",
                    "params": {}
                }
            ],
            "examples": [
                {
                    "method": "download_instagram_media",
                    "request": {
                        "jsonrpc": "2.0",
                        "method": "download_instagram_media",
                        "params": {
                            "url": "https://www.instagram.com/p/DKmRL0ANGZV/",
                            "bot_token": "YOUR_BOT_TOKEN",
                            "chat_id": "YOUR_CHAT_ID"
                        },
                        "id": 1
                    }
                },
                {
                    "method": "get_bot_info",
                    "request": {
                        "jsonrpc": "2.0",
                        "method": "get_bot_info",
                        "params": {
                            "bot_token": "YOUR_BOT_TOKEN"
                        },
                        "id": 2
                    }
                }
            ]
        }
        
        return JsonResponse(documentation, json_dumps_params={'indent': 2})



