# 🚀 SaverAPI JSONRPC Implementation

Bu loyiha uchun JSONRPC 2.0 protokoli asosida API yaratildi. Barcha metodlar JSONRPC formatida ishlaydi.

## 📋 Xususiyatlar

- ✅ JSONRPC 2.0 protokoli
- ✅ Asinxron va sinxron metodlar
- ✅ Instagram media yuklab olish
- ✅ Bot ma'lumotlarini olish va tekshirish
- ✅ Queue holati monitoring
- ✅ Xatoliklarni boshqarish
- ✅ API dokumentatsiyasi

## 🛠️ O'rnatish

1. Kerakli kutubxonani o'rnating:
```bash
pip install jsonrpcserver
```

2. Django settings.py fayliga yangi app qo'shing:
```python
INSTALLED_APPS = [
    # ... boshqa applar
    'jsonrpc_api',
]
```

3. URL konfiguratsiyasini yangilang:
```python
urlpatterns = [
    # ... boshqa URLlar
    path('jsonrpc/', include('jsonrpc_api.urls')),
]
```

## 🌐 Endpoint

**Base URL:** `http://127.0.0.1:8000/jsonrpc/`

### GET Request
API dokumentatsiyasini olish uchun GET so'rov yuboring:
```bash
curl -X GET http://127.0.0.1:8000/jsonrpc/ \
  -H "X-AUTH: your-auth-token"
```

### POST Request
JSONRPC metodlarini chaqirish uchun POST so'rov yuboring:
```bash
curl -X POST http://127.0.0.1:8000/jsonrpc/ \
  -H "Content-Type: application/json" \
  -H "X-AUTH: your-auth-token" \
  -d '{"jsonrpc": "2.0", "method": "method_name", "params": {...}, "id": 1}'
```

## 📚 Mavjud Metodlar

### 1. `download_instagram_media`
Instagram media fayllarini yuklab olish uchun queue ga qo'shadi.

**Parametrlar:**
- `url` (string, majburiy): Instagram URL
- `bot_token` (string, majburiy): Telegram bot token
- `chat_id` (string, majburiy): Telegram chat ID
- `bot_username` (string, ixtiyoriy): Bot username

**Misol:**
```json
{
  "jsonrpc": "2.0",
  "method": "download_instagram_media",
  "params": {
    "url": "https://www.instagram.com/p/DKmRL0ANGZV/",
    "bot_token": "YOUR_BOT_TOKEN",
    "chat_id": "YOUR_CHAT_ID"
  },
  "id": 1
}
```

### 2. `get_bot_info`
Bot haqida ma'lumot olish.

**Parametrlar:**
- `bot_token` (string, majburiy): Telegram bot token
- `use_cache` (boolean, ixtiyoriy): Cache ishlatish (default: true)

**Misol:**
```json
{
  "jsonrpc": "2.0",
  "method": "get_bot_info",
  "params": {
    "bot_token": "YOUR_BOT_TOKEN",
    "use_cache": true
  },
  "id": 2
}
```

### 3. `validate_bot_token`
Bot tokenni tekshirish.

**Parametrlar:**
- `bot_token` (string, majburiy): Tekshiriladigan bot token

**Misol:**
```json
{
  "jsonrpc": "2.0",
  "method": "validate_bot_token",
  "params": {
    "bot_token": "YOUR_BOT_TOKEN"
  },
  "id": 3
}
```

### 4. `get_available_bots`
Mavjud botlar ro'yxatini olish.

**Parametrlar:** Yo'q

**Misol:**
```json
{
  "jsonrpc": "2.0",
  "method": "get_available_bots",
  "params": {},
  "id": 4
}
```

### 5. `get_bot_by_username`
Username bo'yicha bot ma'lumotlarini olish.

**Parametrlar:**
- `username` (string, majburiy): Bot username

**Misol:**
```json
{
  "jsonrpc": "2.0",
  "method": "get_bot_by_username",
  "params": {
    "username": "@your_bot_username"
  },
  "id": 5
}
```

### 6. `get_queue_status`
Queue holatini tekshirish.

**Parametrlar:** Yo'q

**Misol:**
```json
{
  "jsonrpc": "2.0",
  "method": "get_queue_status",
  "params": {},
  "id": 6
}
```

## 📝 Javob Formati

### Muvaffaqiyatli javob:
```json
{
  "jsonrpc": "2.0",
  "result": {
    "success": true,
    "data": {...}
  },
  "id": 1
}
```

### Xatolik javobi:
```json
{
  "jsonrpc": "2.0",
  "result": {
    "success": false,
    "error": "Xatolik tavsifi"
  },
  "id": 1
}
```

## 🧪 Test Qilish

Test skriptini ishga tushiring:
```bash
python test_jsonrpc.py
```

Yoki qo'lda test qiling:
```bash
# API dokumentatsiyasini olish
curl -X GET http://127.0.0.1:8000/jsonrpc/

# Queue holatini tekshirish
curl -X POST http://127.0.0.1:8000/jsonrpc/ \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "get_queue_status",
    "params": {},
    "id": 1
  }'
```

## 🔒 Xavfsizlik

- Barcha so'rovlar uchun `X-AUTH` header talab qilinadi
- CSRF himoyasi o'chirilgan (API uchun)
- Bot tokenlar xavfsiz tarzda ishlanadi

## 📁 Fayl Tuzilishi

```
jsonrpc_api/
├── __init__.py
├── apps.py
├── methods.py      # JSONRPC metodlar
├── views.py        # Django view va JSONRPC dispatch
└── urls.py         # URL konfiguratsiyasi
```

## 🚀 Ishga Tushirish

1. Django serverni ishga tushiring:
```bash
python manage.py runserver
```

2. JSONRPC endpoint mavjud bo'ladi: `http://127.0.0.1:8000/jsonrpc/`

3. Test qiling va foydalaning!

## 🔧 Kengaytirish

Yangi metodlar qo'shish uchun:

1. `jsonrpc_api/methods.py` fayliga yangi metod qo'shing
2. `jsonrpc_api/views.py` fayliga `@method` dekorator bilan metodni ro'yxatdan o'tkazing
3. Agar asinxron metod bo'lsa, `async_methods` ro'yxatiga qo'shing

Misol:
```python
# methods.py da
@staticmethod
def new_method(param1: str) -> Dict[str, Any]:
    # Metod kodi
    pass

# views.py da
@method
def new_method(param1: str) -> dict:
    return JSONRPCMethods.new_method(param1)
```
