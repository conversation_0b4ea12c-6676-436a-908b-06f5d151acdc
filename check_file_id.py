from telebot import TeleBot


bot = TeleBot("8113512069:AAHZhQFm77gKnpocwVz9OS3ugS3GvxLBDRw")

# send video by file_id
@bot.message_handler(commands=['video'])
def send_video(message):
    file_id = "BAACAgQAAxkBAAIDAWholMJ_EiqNGGZpyc8T81c65QnGAAKbCAACpeE8U1usCAQgBrQJHgQ"
    bot.send_video(message.chat.id, file_id)


# send photo by file_id
@bot.message_handler(commands=['photo'])
def send_photo(message):
    file_id = "AgACAgQAAxkBAAIDAWxolM1111111111111111111111111111111111111111111111111111111111111111111111111111111"
    bot.send_photo(message.chat.id, file_id)


bot.infinity_polling()
